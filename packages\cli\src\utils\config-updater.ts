/**
 * Agent configuration update utilities
 */

import type { WidgetGenerationOptions } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class ConfigUpdater {
  private fsManager: FileSystemManager;

  constructor() {
    this.fsManager = new FileSystemManager();
  }

  /**
   * Update agent configuration to include new widget
   */
  async updateAgentConfig(
    configPath: string,
    options: WidgetGenerationOptions,
    widgetImportPath: string,
    widgetCode: string,
  ): Promise<void> {
    const componentName = options.name;

    if (!this.fsManager.fileExists(configPath)) {
      // Create new agent config file
      const newConfigContent = this.generateNewAgentConfig(options, widgetImportPath, widgetCode);
      await this.fsManager.writeFile(configPath, newConfigContent);
      Logger.info("Created new agent-config.tsx file");
    } else {
      // Update existing agent config file
      const existingContent = await this.fsManager.readFile(configPath);
      const updatedContent = this.updateExistingAgentConfig(existingContent, options, widgetImportPath, widgetCode);
      await this.fsManager.updateFile(configPath, updatedContent, true);
      Logger.info("Updated agent-config.tsx file");
    }
  }

  /**
   * Generate new agent config content
   */
  private generateNewAgentConfig(options: WidgetGenerationOptions, importPath: string, widgetCode: string): string {
    const componentName = options.name;

    return `import type { AgentChatConfig } from "@cscs-agent/core";
import ${componentName} from "${importPath}";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Generated Agent",
      code: "generated-agent",
      description: "Agent with generated widgets",
      ${this.generateWidgetPlacement(options, componentName, widgetCode)}
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};
`;
  }

  /**
   * Update existing agent config content
   */
  private updateExistingAgentConfig(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): string {
    const componentName = options.name;

    // Add import statement
    let updatedContent = this.addImportStatement(content, componentName, importPath);

    // Add widget to appropriate section
    updatedContent = this.addWidgetToConfig(updatedContent, options, componentName, widgetCode);

    return updatedContent;
  }

  /**
   * Add import statement to the config file
   */
  private addImportStatement(content: string, componentName: string, importPath: string): string {
    const importStatement = `import ${componentName} from "${importPath}";`;

    // Find the position to insert the import
    const lines = content.split("\n");
    let insertIndex = 0;

    // Find the last import statement
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().startsWith("import ") && lines[i].includes("from ")) {
        insertIndex = i + 1;
      }
    }

    // Insert the new import
    lines.splice(insertIndex, 0, importStatement);

    return lines.join("\n");
  }

  /**
   * Add widget to the appropriate configuration section
   */
  private addWidgetToConfig(
    content: string,
    options: WidgetGenerationOptions,
    componentName: string,
    widgetCode: string,
  ): string {
    const widgetConfig = `        {
          code: "${widgetCode}",
          component: ${componentName},
        }`;

    // Determine the target section based on placement and slot
    const targetSection = this.getTargetSection(options);

    // Try to find and update the existing section
    const sectionRegex = this.getSectionRegex(targetSection);
    const match = content.match(sectionRegex);

    if (match) {
      // Section exists, add widget to it
      return this.addWidgetToExistingSection(content, widgetConfig, targetSection);
    } else {
      // Section doesn't exist, create it
      return this.createNewSection(content, options, componentName, widgetCode);
    }
  }

  /**
   * Get target section path based on placement and slot
   */
  private getTargetSection(options: WidgetGenerationOptions): string {
    if (options.placement === "message") {
      if (options.slot === "blocks") {
        return "message.blocks.widgets";
      } else {
        return `message.slots.${options.slot}.widgets`;
      }
    } else if (options.placement === "sender") {
      return `sender.slots.${options.slot}.widgets`;
    } else if (options.placement === "sidePanel") {
      return "sidePanel.render.widgets";
    }

    return "message.blocks.widgets"; // default
  }

  /**
   * Get regex pattern for finding a section
   */
  private getSectionRegex(section: string): RegExp {
    const parts = section.split(".");
    let pattern = "";

    for (let i = 0; i < parts.length; i++) {
      if (i === parts.length - 1) {
        // Last part should be 'widgets'
        pattern += `${parts[i]}:\\s*\\[`;
      } else {
        pattern += `${parts[i]}:\\s*\\{[^}]*`;
      }
    }

    return new RegExp(pattern, "s");
  }

  /**
   * Add widget to existing section
   */
  private addWidgetToExistingSection(content: string, widgetConfig: string, section: string): string {
    // Find the widgets array and add the new widget
    const widgetsRegex = /widgets:\s*\[\s*/g;
    let match;
    let lastMatch = null;

    while ((match = widgetsRegex.exec(content)) !== null) {
      lastMatch = match;
    }

    if (lastMatch) {
      const insertPosition = lastMatch.index + lastMatch[0].length;
      return content.slice(0, insertPosition) + widgetConfig + ",\n" + content.slice(insertPosition);
    }

    return content;
  }

  /**
   * Create new section with widget
   */
  private createNewSection(
    content: string,
    options: WidgetGenerationOptions,
    componentName: string,
    widgetCode: string,
  ): string {
    const sectionContent = this.generateWidgetPlacement(options, componentName, widgetCode);

    // Find the agent configuration and add the new section
    const agentRegex = /(\{[^}]*name:\s*["'][^"']*["'][^}]*code:\s*["'][^"']*["'][^}]*)/s;
    const match = content.match(agentRegex);

    if (match && match.index !== undefined) {
      const insertPosition = match.index + match[0].length;
      return content.slice(0, insertPosition) + "\n      " + sectionContent + content.slice(insertPosition);
    }

    return content;
  }

  /**
   * Generate widget placement configuration
   */
  private generateWidgetPlacement(options: WidgetGenerationOptions, componentName: string, widgetCode: string): string {
    const widgetConfig = `{
          code: "${widgetCode}",
          component: ${componentName},
        }`;

    if (options.placement === "message") {
      if (options.slot === "blocks") {
        return `message: {
        blocks: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
      } else {
        return `message: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
      }
    } else if (options.placement === "sender") {
      return `sender: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
    } else if (options.placement === "sidePanel") {
      return `sidePanel: {
        render: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
    }

    return "";
  }
}
